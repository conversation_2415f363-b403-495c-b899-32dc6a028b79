# -*- coding: utf-8 -*-
"""
QS大学排名爬虫 - 主要爬取逻辑
"""

import requests
import time
import json
import re
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging
from config import SCRAPER_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSScraper:
    """QS大学排名爬虫类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(SCRAPER_CONFIG["headers"])
        self.base_url = SCRAPER_CONFIG["base_url"]
        self.driver = None
        
    def setup_selenium_driver(self):
        """设置Selenium WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument(f'--user-agent={SCRAPER_CONFIG["headers"]["User-Agent"]}')
            
            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            logger.info("Selenium WebDriver 设置成功")
            return True
            
        except Exception as e:
            logger.error(f"设置Selenium WebDriver失败: {e}")
            return False
    
    def close_driver(self):
        """关闭WebDriver"""
        if self.driver:
            self.driver.quit()
            self.driver = None
    
    def scrape_with_requests(self):
        """使用requests库尝试爬取数据"""
        try:
            logger.info("尝试使用requests库爬取数据...")
            response = self.session.get(self.base_url, timeout=SCRAPER_CONFIG["timeout"])
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找排名表格
            universities = []
            
            # 尝试不同的选择器来找到大学数据
            selectors = [
                'table.ranking-table tr',
                '.ranking-item',
                '.university-item',
                '[data-university]',
                '.ranking-row'
            ]
            
            for selector in selectors:
                elements = soup.select(selector)
                if elements:
                    logger.info(f"找到 {len(elements)} 个元素，使用选择器: {selector}")
                    universities = self._parse_university_elements(elements)
                    if universities:
                        break
            
            if not universities:
                logger.warning("使用requests未能找到大学数据，可能需要JavaScript渲染")
                return []
            
            return universities
            
        except Exception as e:
            logger.error(f"使用requests爬取失败: {e}")
            return []
    
    def scrape_with_selenium(self):
        """使用Selenium爬取数据"""
        try:
            if not self.setup_selenium_driver():
                return []
            
            logger.info("使用Selenium爬取数据...")
            self.driver.get(self.base_url)
            
            # 等待页面加载
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 尝试点击"显示更多"按钮或加载所有数据
            self._load_all_universities()
            
            # 获取页面源码
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 解析大学数据
            universities = self._parse_university_data_selenium(soup)
            
            return universities
            
        except Exception as e:
            logger.error(f"使用Selenium爬取失败: {e}")
            return []
        finally:
            self.close_driver()
    
    def _load_all_universities(self):
        """加载所有大学数据"""
        try:
            # 尝试滚动到页面底部以触发懒加载
            last_height = self.driver.execute_script("return document.body.scrollHeight")
            
            while True:
                # 滚动到页面底部
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                
                # 等待新内容加载
                time.sleep(2)
                
                # 检查是否有"加载更多"按钮
                load_more_buttons = self.driver.find_elements(By.XPATH, 
                    "//button[contains(text(), '加载更多') or contains(text(), 'Load More') or contains(text(), '显示更多')]")
                
                if load_more_buttons:
                    for button in load_more_buttons:
                        try:
                            if button.is_displayed() and button.is_enabled():
                                self.driver.execute_script("arguments[0].click();", button)
                                time.sleep(3)
                        except:
                            pass
                
                # 检查页面高度是否改变
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    break
                last_height = new_height
                
                # 防止无限循环
                if last_height > 50000:  # 假设页面不会超过50000px
                    break
            
            logger.info("完成页面滚动和数据加载")
            
        except Exception as e:
            logger.error(f"加载所有大学数据时出错: {e}")
    
    def _parse_university_data_selenium(self, soup):
        """解析Selenium获取的大学数据"""
        universities = []
        
        try:
            # 尝试多种选择器来找到大学数据
            selectors = [
                'tr[data-rank]',
                '.ranking-table tbody tr',
                '.university-row',
                '[data-university-name]',
                'tr:has(.university-name)',
                'tr:has(.rank)',
            ]
            
            for selector in selectors:
                elements = soup.select(selector)
                if elements and len(elements) > 10:  # 确保找到足够多的数据
                    logger.info(f"使用选择器 '{selector}' 找到 {len(elements)} 个大学")
                    universities = self._parse_selenium_elements(elements)
                    if universities:
                        break
            
            # 如果上述方法都失败，尝试解析表格
            if not universities:
                universities = self._parse_table_data(soup)
            
            return universities
            
        except Exception as e:
            logger.error(f"解析Selenium数据时出错: {e}")
            return []
    
    def _parse_selenium_elements(self, elements):
        """解析Selenium找到的元素"""
        universities = []
        
        for i, element in enumerate(elements[:1000]):  # 限制为前1000个
            try:
                # 提取排名
                rank_elem = element.select_one('.rank, [data-rank], .ranking-position, td:first-child')
                rank = 0
                if rank_elem:
                    rank_text = rank_elem.get_text(strip=True)
                    rank_match = re.search(r'\d+', rank_text)
                    if rank_match:
                        rank = int(rank_match.group())
                
                # 提取大学名称
                name_elem = element.select_one('.university-name, .uni-name, [data-university-name], .institution-name, td:nth-child(2)')
                name = ""
                if name_elem:
                    name = name_elem.get_text(strip=True)
                
                # 提取国家
                country_elem = element.select_one('.country, .location, .nation, td:nth-child(3)')
                country = ""
                if country_elem:
                    country = country_elem.get_text(strip=True)
                
                # 如果找到了基本信息，添加到列表
                if name and rank > 0:
                    university_data = {
                        "name": name,
                        "qs_ranking": rank,
                        "country": country,
                        "official_website": "",
                        "college_website": ""
                    }
                    universities.append(university_data)
                
            except Exception as e:
                logger.error(f"解析第 {i+1} 个大学元素时出错: {e}")
                continue
        
        logger.info(f"成功解析 {len(universities)} 所大学")
        return universities
    
    def _parse_table_data(self, soup):
        """解析表格数据"""
        universities = []
        
        try:
            # 查找表格
            tables = soup.find_all('table')
            
            for table in tables:
                rows = table.find_all('tr')
                if len(rows) < 10:  # 跳过太小的表格
                    continue
                
                for i, row in enumerate(rows[1:1001]):  # 跳过表头，限制1000行
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        try:
                            # 假设第一列是排名，第二列是大学名称
                            rank_text = cells[0].get_text(strip=True)
                            name_text = cells[1].get_text(strip=True)
                            
                            rank_match = re.search(r'\d+', rank_text)
                            if rank_match and name_text:
                                rank = int(rank_match.group())
                                
                                # 尝试获取国家信息
                                country = ""
                                if len(cells) > 2:
                                    country = cells[2].get_text(strip=True)
                                
                                university_data = {
                                    "name": name_text,
                                    "qs_ranking": rank,
                                    "country": country,
                                    "official_website": "",
                                    "college_website": ""
                                }
                                universities.append(university_data)
                                
                        except Exception as e:
                            continue
                
                if universities:
                    break
            
            logger.info(f"从表格解析出 {len(universities)} 所大学")
            return universities
            
        except Exception as e:
            logger.error(f"解析表格数据时出错: {e}")
            return []
    
    def _parse_university_elements(self, elements):
        """解析大学元素（requests方法）"""
        universities = []
        
        for element in elements:
            try:
                # 这里需要根据实际的HTML结构来调整
                name = element.get_text(strip=True)
                if name:
                    universities.append({
                        "name": name,
                        "qs_ranking": len(universities) + 1,
                        "country": "",
                        "official_website": "",
                        "college_website": ""
                    })
            except:
                continue
        
        return universities
    
    def scrape_universities(self):
        """主要的爬取方法"""
        logger.info("开始爬取QS大学排名数据...")
        
        # 首先尝试使用requests
        universities = self.scrape_with_requests()
        
        # 如果requests失败，使用Selenium
        if not universities:
            logger.info("requests方法失败，尝试使用Selenium...")
            universities = self.scrape_with_selenium()
        
        if universities:
            logger.info(f"成功爬取 {len(universities)} 所大学的数据")
        else:
            logger.error("爬取失败，未获取到任何大学数据")
        
        return universities
