# -*- coding: utf-8 -*-
"""
QS大学排名爬虫主程序
"""

import json
import logging
import sys
from datetime import datetime
from qs_scraper import QSS<PERSON>raper
from data_processor import DataProcessor
from config import OUTPUT_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('qs_scraper.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def save_to_json(data, filename):
    """
    保存数据到JSON文件
    
    Args:
        data (list): 要保存的数据
        filename (str): 文件名
    """
    try:
        with open(filename, 'w', encoding=OUTPUT_CONFIG["encoding"]) as f:
            json.dump(data, f, 
                     ensure_ascii=OUTPUT_CONFIG["ensure_ascii"], 
                     indent=OUTPUT_CONFIG["indent"])
        logger.info(f"数据已保存到 {filename}")
    except Exception as e:
        logger.error(f"保存数据到JSON文件失败: {e}")

def create_sample_data():
    """
    创建示例数据（用于测试）
    """
    sample_data = [
        {
            "name": "麻省理工学院",
            "abbreviation": "MIT",
            "qs_ranking": 1,
            "country": "United States",
            "continent": "North America",
            "official_website": "https://mit.edu",
            "college_website": "https://catalog.mit.edu"
        },
        {
            "name": "剑桥大学",
            "abbreviation": "Cambridge",
            "qs_ranking": 5,
            "country": "United Kingdom",
            "continent": "Europe",
            "official_website": "https://www.cam.ac.uk",
            "college_website": "https://www.cam.ac.uk"
        },
        {
            "name": "斯坦福大学",
            "abbreviation": "Stanford",
            "qs_ranking": 2,
            "country": "United States",
            "continent": "North America",
            "official_website": "https://www.stanford.edu",
            "college_website": "https://www.stanford.edu/academics"
        },
        {
            "name": "哈佛大学",
            "abbreviation": "Harvard",
            "qs_ranking": 4,
            "country": "United States",
            "continent": "North America",
            "official_website": "https://www.harvard.edu",
            "college_website": "https://www.harvard.edu/schools"
        },
        {
            "name": "牛津大学",
            "abbreviation": "Oxford",
            "qs_ranking": 3,
            "country": "United Kingdom",
            "continent": "Europe",
            "official_website": "https://www.ox.ac.uk",
            "college_website": "https://www.ox.ac.uk/admissions"
        }
    ]
    return sample_data

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("QS大学排名爬虫程序启动")
    logger.info(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 50)
    
    try:
        # 初始化爬虫
        scraper = QSScraper()
        
        # 爬取大学数据
        logger.info("步骤1: 爬取大学基础数据...")
        universities_raw = scraper.scrape_universities()
        
        if not universities_raw:
            logger.warning("未能爬取到大学数据，使用示例数据进行演示...")
            universities_raw = create_sample_data()
        
        logger.info(f"获取到 {len(universities_raw)} 所大学的基础数据")
        
        # 初始化数据处理器
        processor = DataProcessor()
        
        # 处理数据
        logger.info("步骤2: 处理和补充大学数据...")
        universities_processed = processor.batch_process_universities(
            universities_raw, 
            delay=1  # 1秒延迟，避免请求过于频繁
        )
        
        if not universities_processed:
            logger.error("数据处理失败")
            return
        
        logger.info(f"成功处理 {len(universities_processed)} 所大学的数据")
        
        # 按排名排序
        universities_processed.sort(key=lambda x: x.get('qs_ranking', 9999))
        
        # 限制为前1000名
        universities_top1000 = universities_processed[:1000]
        
        # 保存数据
        logger.info("步骤3: 保存数据到JSON文件...")
        save_to_json(universities_top1000, OUTPUT_CONFIG["json_file"])
        
        # 显示统计信息
        logger.info("=" * 50)
        logger.info("爬取完成统计:")
        logger.info(f"总共处理大学数量: {len(universities_top1000)}")
        
        # 按洲际统计
        continent_stats = {}
        for uni in universities_top1000:
            continent = uni.get('continent', 'Unknown')
            continent_stats[continent] = continent_stats.get(continent, 0) + 1
        
        logger.info("按洲际分布:")
        for continent, count in sorted(continent_stats.items()):
            logger.info(f"  {continent}: {count} 所")
        
        # 显示前10名
        logger.info("\n前10名大学:")
        for i, uni in enumerate(universities_top1000[:10]):
            logger.info(f"  {i+1}. {uni['name']} ({uni['country']}) - 排名: {uni['qs_ranking']}")
        
        logger.info("=" * 50)
        logger.info(f"程序执行完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"数据已保存到: {OUTPUT_CONFIG['json_file']}")
        
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
