# -*- coding: utf-8 -*-
"""
数据处理模块 - 处理大学信息的各种转换和补充
"""

import re
import requests
from urllib.parse import urljoin, urlparse
from config import COUNTRY_CONTINENT_MAP, UNIVERSITY_ABBREVIATIONS
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataProcessor:
    """数据处理器类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
    
    def get_continent_by_country(self, country):
        """
        根据国家获取洲际信息
        
        Args:
            country (str): 国家名称
            
        Returns:
            str: 洲际名称
        """
        return COUNTRY_CONTINENT_MAP.get(country, "Unknown")
    
    def get_university_abbreviation(self, university_name):
        """
        获取大学缩写
        
        Args:
            university_name (str): 大学全名
            
        Returns:
            str: 大学缩写
        """
        # 首先检查预定义的缩写
        if university_name in UNIVERSITY_ABBREVIATIONS:
            return UNIVERSITY_ABBREVIATIONS[university_name]
        
        # 如果没有预定义缩写，尝试生成一个
        return self._generate_abbreviation(university_name)
    
    def _generate_abbreviation(self, university_name):
        """
        生成大学缩写
        
        Args:
            university_name (str): 大学全名
            
        Returns:
            str: 生成的缩写
        """
        # 移除常见的大学词汇
        name = university_name.replace("University of ", "")
        name = name.replace("University", "")
        name = name.replace("College", "")
        name = name.replace("Institute of Technology", "")
        name = name.replace("Institute", "")
        name = name.replace("School", "")
        name = name.strip()
        
        # 如果名称很短，直接返回
        if len(name) <= 10:
            return name
        
        # 提取首字母
        words = name.split()
        if len(words) >= 2:
            abbreviation = ''.join([word[0].upper() for word in words if word and word[0].isalpha()])
            if len(abbreviation) >= 2:
                return abbreviation
        
        # 如果无法生成合适的缩写，返回前几个字符
        return name[:8] if len(name) > 8 else name
    
    def find_official_website(self, university_name, country=None):
        """
        查找大学官方网站
        
        Args:
            university_name (str): 大学名称
            country (str): 国家名称（可选）
            
        Returns:
            str: 官方网站URL
        """
        try:
            # 构建搜索查询
            search_query = f"{university_name} official website"
            if country:
                search_query += f" {country}"
            
            # 这里可以使用Google搜索API或其他方法
            # 为了简化，我们返回一个占位符
            # 在实际应用中，可以集成搜索API
            
            # 尝试一些常见的域名模式
            common_patterns = self._generate_common_domain_patterns(university_name)
            
            for pattern in common_patterns:
                if self._check_website_exists(pattern):
                    return pattern
            
            return ""
            
        except Exception as e:
            logger.error(f"查找官方网站时出错 {university_name}: {e}")
            return ""
    
    def _generate_common_domain_patterns(self, university_name):
        """
        生成常见的域名模式
        
        Args:
            university_name (str): 大学名称
            
        Returns:
            list: 可能的域名列表
        """
        patterns = []
        
        # 清理大学名称
        clean_name = university_name.lower()
        clean_name = re.sub(r'[^a-z\s]', '', clean_name)
        clean_name = clean_name.replace('university of ', '')
        clean_name = clean_name.replace('university', '')
        clean_name = clean_name.replace('college', '')
        clean_name = clean_name.replace('institute of technology', '')
        clean_name = clean_name.replace('institute', '')
        clean_name = clean_name.replace('school', '')
        clean_name = clean_name.strip()
        
        # 生成可能的域名
        words = clean_name.split()
        if words:
            # 单词组合
            if len(words) == 1:
                patterns.extend([
                    f"https://www.{words[0]}.edu",
                    f"https://www.{words[0]}.ac.uk",
                    f"https://www.{words[0]}.edu.au",
                    f"https://{words[0]}.edu",
                ])
            elif len(words) >= 2:
                # 前两个单词
                combined = ''.join(words[:2])
                patterns.extend([
                    f"https://www.{combined}.edu",
                    f"https://www.{words[0]}{words[1]}.edu",
                    f"https://www.{words[0]}.{words[1]}.edu",
                ])
                
                # 首字母缩写
                abbreviation = ''.join([w[0] for w in words])
                patterns.extend([
                    f"https://www.{abbreviation}.edu",
                    f"https://www.{abbreviation}.ac.uk",
                ])
        
        return patterns[:10]  # 限制检查数量
    
    def _check_website_exists(self, url):
        """
        检查网站是否存在
        
        Args:
            url (str): 网站URL
            
        Returns:
            bool: 网站是否存在
        """
        try:
            response = self.session.head(url, timeout=5, allow_redirects=True)
            return response.status_code == 200
        except:
            return False
    
    def find_college_website(self, official_website, university_name):
        """
        查找学院/专业信息网站
        
        Args:
            official_website (str): 官方网站
            university_name (str): 大学名称
            
        Returns:
            str: 学院网站URL
        """
        if not official_website:
            return ""
        
        try:
            # 常见的学院/专业页面路径
            common_paths = [
                "/academics",
                "/schools",
                "/colleges",
                "/departments",
                "/programs",
                "/study",
                "/courses",
                "/faculties",
                "/schools-colleges",
                "/academic-departments",
                "/undergraduate",
                "/graduate",
                "/admissions/academic-programs",
            ]
            
            base_url = official_website.rstrip('/')
            
            for path in common_paths:
                test_url = base_url + path
                if self._check_website_exists(test_url):
                    return test_url
            
            # 如果没有找到特定页面，返回官方网站
            return official_website
            
        except Exception as e:
            logger.error(f"查找学院网站时出错 {university_name}: {e}")
            return official_website
    
    def clean_university_name(self, name):
        """
        清理大学名称
        
        Args:
            name (str): 原始大学名称
            
        Returns:
            str: 清理后的大学名称
        """
        if not name:
            return ""
        
        # 移除多余的空格和特殊字符
        name = re.sub(r'\s+', ' ', name.strip())
        
        # 移除排名信息（如果存在）
        name = re.sub(r'^\d+\.\s*', '', name)
        name = re.sub(r'^\d+\s+', '', name)
        
        return name
    
    def process_university_data(self, raw_data):
        """
        处理单个大学的原始数据
        
        Args:
            raw_data (dict): 原始大学数据
            
        Returns:
            dict: 处理后的大学数据
        """
        try:
            # 清理大学名称
            university_name = self.clean_university_name(raw_data.get('name', ''))
            
            # 获取缩写
            abbreviation = self.get_university_abbreviation(university_name)
            
            # 获取国家和洲际
            country = raw_data.get('country', '')
            continent = self.get_continent_by_country(country)
            
            # 查找官方网站
            official_website = raw_data.get('official_website', '') or self.find_official_website(university_name, country)
            
            # 查找学院网站
            college_website = self.find_college_website(official_website, university_name)
            
            # 构建最终数据
            processed_data = {
                "name": university_name,
                "abbreviation": abbreviation,
                "qs_ranking": raw_data.get('qs_ranking', 0),
                "country": country,
                "continent": continent,
                "official_website": official_website,
                "college_website": college_website
            }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"处理大学数据时出错: {e}")
            return None
    
    def batch_process_universities(self, universities_data, delay=1):
        """
        批量处理大学数据
        
        Args:
            universities_data (list): 大学数据列表
            delay (int): 请求间隔（秒）
            
        Returns:
            list: 处理后的大学数据列表
        """
        processed_universities = []
        
        for i, university in enumerate(universities_data):
            logger.info(f"处理第 {i+1}/{len(universities_data)} 所大学: {university.get('name', 'Unknown')}")
            
            processed = self.process_university_data(university)
            if processed:
                processed_universities.append(processed)
            
            # 添加延迟以避免过于频繁的请求
            if delay > 0 and i < len(universities_data) - 1:
                time.sleep(delay)
        
        return processed_universities
