# -*- coding: utf-8 -*-
"""
QS Requests爬虫 - 使用requests库获取QS真实排名数据
"""

import requests
import json
import re
import logging
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from data_processor import DataProcessor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSRequestsScraper:
    """QS Requests爬虫类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': 'https://www.qschina.cn/',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        })
        self.base_url = "https://www.qschina.cn/university-rankings/world-university-rankings/2025"
        
    def scrape_real_rankings(self):
        """爬取真实的QS排名数据"""
        logger.info("开始分析QS网站结构...")
        
        try:
            # 1. 首先获取主页面
            response = self.session.get(self.base_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 2. 查找可能的API端点
            api_urls = self._find_api_endpoints(response.text, soup)
            
            # 3. 尝试从API获取数据
            universities = self._try_api_endpoints(api_urls)
            
            if not universities:
                # 4. 尝试从页面HTML中提取数据
                universities = self._extract_from_html(soup)
            
            if not universities:
                # 5. 分析JavaScript文件
                universities = self._analyze_js_files(soup)
            
            if not universities:
                # 6. 尝试模拟AJAX请求
                universities = self._try_ajax_requests()
            
            return universities[:100]  # 只返回前100名
            
        except Exception as e:
            logger.error(f"爬取过程中出错: {e}")
            return []
    
    def _find_api_endpoints(self, html_content, soup):
        """查找可能的API端点"""
        api_urls = []
        
        try:
            # 1. 从script标签中查找API URL
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string:
                    # 查找API URL模式
                    url_patterns = [
                        r'["\']https?://[^"\']*api[^"\']*["\']',
                        r'["\']https?://[^"\']*ranking[^"\']*\.json["\']',
                        r'["\']https?://[^"\']*university[^"\']*\.json["\']',
                        r'["\'][^"\']*rankings[^"\']*\.json["\']',
                        r'["\'][^"\']*data[^"\']*\.json["\']',
                        r'url\s*:\s*["\']([^"\']+)["\']',
                        r'endpoint\s*:\s*["\']([^"\']+)["\']',
                    ]
                    
                    for pattern in url_patterns:
                        matches = re.findall(pattern, script.string, re.IGNORECASE)
                        for match in matches:
                            url = match.strip('"\'')
                            if url and ('ranking' in url.lower() or 'university' in url.lower() or 'data' in url.lower()):
                                if url.startswith('/'):
                                    url = 'https://www.qschina.cn' + url
                                api_urls.append(url)
            
            # 2. 查找data属性中的URL
            elements_with_data = soup.find_all(attrs=lambda x: x and any('url' in k.lower() for k in x.keys()))
            for elem in elements_with_data:
                for attr, value in elem.attrs.items():
                    if 'url' in attr.lower() and isinstance(value, str):
                        if 'ranking' in value.lower() or 'university' in value.lower():
                            if value.startswith('/'):
                                value = 'https://www.qschina.cn' + value
                            api_urls.append(value)
            
            # 3. 常见的API端点模式
            common_endpoints = [
                '/api/rankings/world-university-rankings/2025',
                '/api/rankings/2025',
                '/api/universities/rankings/2025',
                '/sites/default/files/qs-rankings/2025/qs-world-university-rankings-2025.json',
                '/sites/default/files/rankings/2025/world-university-rankings.json',
                '/data/rankings/2025.json',
                '/data/world-university-rankings-2025.json',
                '/rankings/data/2025.json',
                '/university-rankings/data/2025.json',
                '/ajax/rankings/world-university-rankings/2025',
            ]
            
            for endpoint in common_endpoints:
                api_urls.append('https://www.qschina.cn' + endpoint)
            
            # 4. 尝试topuniversities.com的端点
            tu_endpoints = [
                'https://www.topuniversities.com/sites/default/files/qs-rankings/2025/qs-world-university-rankings-2025.json',
                'https://www.topuniversities.com/api/rankings/world-university-rankings/2025',
                'https://www.topuniversities.com/data/rankings/2025.json',
            ]
            api_urls.extend(tu_endpoints)
            
            # 去重
            api_urls = list(set(api_urls))
            logger.info(f"找到 {len(api_urls)} 个可能的API端点")
            
            return api_urls
            
        except Exception as e:
            logger.error(f"查找API端点时出错: {e}")
            return []
    
    def _try_api_endpoints(self, api_urls):
        """尝试API端点"""
        universities = []
        
        for i, url in enumerate(api_urls):
            try:
                logger.info(f"测试API端点 {i+1}/{len(api_urls)}: {url}")
                
                headers = self.session.headers.copy()
                headers.update({
                    'Accept': 'application/json, text/plain, */*',
                    'X-Requested-With': 'XMLHttpRequest'
                })
                
                response = self.session.get(url, headers=headers, timeout=15)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if self._is_valid_university_data(data):
                            logger.info(f"✅ 找到有效数据: {url}")
                            universities = self._process_api_data(data)
                            if len(universities) >= 50:  # 确保有足够的数据
                                return universities
                    except json.JSONDecodeError:
                        # 可能是HTML或其他格式
                        if 'university' in response.text.lower():
                            logger.info(f"⚠️  端点返回HTML内容: {url}")
                
                time.sleep(0.5)  # 避免请求过快
                
            except Exception as e:
                logger.debug(f"API端点请求失败: {url} - {e}")
                continue
        
        return universities
    
    def _is_valid_university_data(self, data):
        """检查是否是有效的大学数据"""
        try:
            if isinstance(data, list):
                if len(data) > 10:
                    first_item = data[0]
                    if isinstance(first_item, dict):
                        university_fields = ['name', 'university', 'institution', 'rank', 'ranking']
                        return any(field in str(first_item).lower() for field in university_fields)
            elif isinstance(data, dict):
                if 'universities' in data or 'rankings' in data:
                    return True
                university_fields = ['name', 'university', 'institution', 'rank', 'ranking']
                return any(field in str(data).lower() for field in university_fields)
            
            return False
        except:
            return False
    
    def _process_api_data(self, data):
        """处理API数据"""
        universities = []
        
        try:
            if isinstance(data, list):
                source_data = data
            elif isinstance(data, dict):
                if 'universities' in data:
                    source_data = data['universities']
                elif 'rankings' in data:
                    source_data = data['rankings']
                elif 'data' in data:
                    source_data = data['data']
                else:
                    source_data = [data]
            else:
                return []
            
            for item in source_data:
                if isinstance(item, dict):
                    name = self._extract_field(item, ['name', 'university_name', 'institution', 'title'])
                    rank = self._extract_field(item, ['rank', 'ranking', 'position', 'rank_display'])
                    country = self._extract_field(item, ['country', 'location', 'nation'])
                    score = self._extract_field(item, ['score', 'overall_score', 'total_score'])
                    
                    if name:
                        universities.append({
                            "name": str(name).strip(),
                            "qs_ranking": int(rank) if rank and str(rank).replace('.', '').isdigit() else len(universities) + 1,
                            "country": str(country).strip() if country else "",
                            "score": str(score) if score else "",
                            "official_website": "",
                            "college_website": ""
                        })
            
            # 按排名排序
            universities.sort(key=lambda x: x['qs_ranking'])
            
            logger.info(f"从API数据中处理了 {len(universities)} 所大学")
            return universities
            
        except Exception as e:
            logger.error(f"处理API数据时出错: {e}")
            return []
    
    def _extract_field(self, item, field_names):
        """从字典中提取字段值"""
        for field in field_names:
            if field in item:
                return item[field]
            if field.lower() in item:
                return item[field.lower()]
            if field.upper() in item:
                return item[field.upper()]
        return None
    
    def _extract_from_html(self, soup):
        """从HTML中提取数据"""
        logger.info("尝试从HTML中提取排名数据...")
        universities = []
        
        try:
            # 查找可能包含排名数据的表格或列表
            selectors = [
                'table tbody tr',
                'table tr',
                '.ranking-table tr',
                '.university-ranking tr',
                '.ranking-item',
                '.university-item',
                '.ranking-row',
                '.university-row',
                '[data-rank]',
                '[data-university]',
                '.ranking-list > div',
                '.university-list > div',
            ]
            
            for selector in selectors:
                elements = soup.select(selector)
                if elements and len(elements) > 5:
                    logger.info(f"使用选择器 '{selector}' 找到 {len(elements)} 个元素")
                    universities = self._parse_html_elements(elements)
                    if len(universities) >= 10:
                        break
            
            return universities
            
        except Exception as e:
            logger.error(f"从HTML提取数据时出错: {e}")
            return []
    
    def _parse_html_elements(self, elements):
        """解析HTML元素"""
        universities = []
        
        for i, element in enumerate(elements[:100]):
            try:
                text = element.get_text(strip=True)
                if not text or len(text) < 5:
                    continue
                
                lines = [line.strip() for line in text.split('\n') if line.strip()]
                
                # 提取排名
                rank = i + 1
                for line in lines:
                    rank_match = re.search(r'^(\d+)', line)
                    if rank_match:
                        rank = int(rank_match.group(1))
                        break
                
                # 提取大学名称
                name = ""
                for line in lines:
                    if any(keyword in line for keyword in ['University', 'College', 'Institute', 'School']):
                        if len(line) > 5 and len(line) < 100:
                            name = line
                            break
                
                # 提取国家
                country = ""
                common_countries = ['United States', 'United Kingdom', 'China', 'Germany', 'Australia', 'Canada', 'Japan', 'France', 'Singapore', 'Switzerland']
                for line in lines:
                    for c in common_countries:
                        if c.lower() in line.lower():
                            country = c
                            break
                    if country:
                        break
                
                if name and rank <= 100:
                    universities.append({
                        "name": name,
                        "qs_ranking": rank,
                        "country": country,
                        "score": "",
                        "official_website": "",
                        "college_website": ""
                    })
                    
            except Exception as e:
                logger.debug(f"解析第 {i} 个元素时出错: {e}")
                continue
        
        universities.sort(key=lambda x: x['qs_ranking'])
        logger.info(f"从HTML中提取了 {len(universities)} 所大学")
        return universities
    
    def _analyze_js_files(self, soup):
        """分析JavaScript文件"""
        logger.info("分析JavaScript文件...")
        universities = []
        
        try:
            scripts = soup.find_all('script', src=True)
            for script in scripts[:5]:  # 只分析前5个JS文件
                src = script.get('src')
                if src:
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = 'https://www.qschina.cn' + src
                    
                    try:
                        response = self.session.get(src, timeout=10)
                        if response.status_code == 200:
                            js_content = response.text
                            universities = self._extract_from_js_content(js_content)
                            if universities:
                                return universities
                    except:
                        continue
            
            return universities
            
        except Exception as e:
            logger.error(f"分析JavaScript文件时出错: {e}")
            return []
    
    def _extract_from_js_content(self, js_content):
        """从JavaScript内容中提取数据"""
        try:
            # 查找可能的数据结构
            patterns = [
                r'var\s+rankingData\s*=\s*(\[.*?\]);',
                r'window\.rankingData\s*=\s*(\[.*?\]);',
                r'rankings\s*:\s*(\[.*?\])',
                r'universities\s*:\s*(\[.*?\])',
                r'"universities":\s*(\[.*?\])',
                r'"rankings":\s*(\[.*?\])',
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, js_content, re.DOTALL)
                for match in matches:
                    try:
                        data = json.loads(match)
                        if isinstance(data, list) and len(data) > 10:
                            return self._process_api_data(data)
                    except json.JSONDecodeError:
                        continue
            
            return []
            
        except Exception as e:
            logger.error(f"从JavaScript内容提取数据时出错: {e}")
            return []
    
    def _try_ajax_requests(self):
        """尝试AJAX请求"""
        logger.info("尝试AJAX请求...")
        
        ajax_endpoints = [
            '/ajax/rankings',
            '/api/rankings',
            '/rankings/ajax',
            '/university-rankings/ajax',
        ]
        
        for endpoint in ajax_endpoints:
            try:
                url = 'https://www.qschina.cn' + endpoint
                headers = self.session.headers.copy()
                headers.update({
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                })
                
                response = self.session.get(url, headers=headers, timeout=10)
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if self._is_valid_university_data(data):
                            return self._process_api_data(data)
                    except json.JSONDecodeError:
                        pass
            except:
                continue
        
        return []

def main():
    """主函数"""
    logger.info("开始使用requests爬取QS真实Top100排名数据...")
    
    scraper = QSRequestsScraper()
    universities = scraper.scrape_real_rankings()
    
    if universities:
        logger.info(f"成功获取 {len(universities)} 所大学的数据")
        
        # 处理数据
        processor = DataProcessor()
        logger.info("开始处理大学数据...")
        processed_universities = processor.batch_process_universities(universities, delay=0.1)
        
        # 保存数据
        output_file = "qs_real_top100_requests.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_universities, f, ensure_ascii=False, indent=2)
        
        logger.info(f"数据已保存到 {output_file}")
        
        # 显示前10名
        logger.info("\n前10名大学:")
        for i, uni in enumerate(processed_universities[:10]):
            logger.info(f"  {i+1}. {uni['name']} ({uni['country']}) - 排名: {uni['qs_ranking']}")
        
        print(f"\n✅ 成功爬取 {len(processed_universities)} 所大学的真实QS排名数据")
        print(f"📁 数据已保存到: {output_file}")
        
    else:
        logger.warning("未能获取到真实数据，可能需要使用Selenium或其他方法")
        print("⚠️  未能获取到真实数据，QS网站可能使用了复杂的JavaScript加载机制")

if __name__ == "__main__":
    main()
