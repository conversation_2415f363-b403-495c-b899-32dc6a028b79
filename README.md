# QS大学排名爬虫

这是一个用于爬取QS全球Top1000高校信息的Python程序。

## 功能特点

- 🎯 爬取QS官网最新的全球大学排名数据
- 🌍 自动识别大学所属洲际
- 🔗 查找大学官方网站和学院网站
- 📊 生成标准化的JSON格式数据
- 🛡️ 多种爬取策略，确保数据获取成功
- 📝 详细的日志记录

## 数据格式

输出的JSON数据格式如下：

```json
[
  {
    "name": "麻省理工学院",
    "abbreviation": "MIT",
    "qs_ranking": 1,
    "country": "United States",
    "continent": "North America",
    "official_website": "https://mit.edu",
    "college_website": "https://catalog.mit.edu"
  },
  {
    "name": "剑桥大学",
    "abbreviation": "Cambridge",
    "qs_ranking": 5,
    "country": "United Kingdom",
    "continent": "Europe",
    "official_website": "https://www.cam.ac.uk",
    "college_website": "https://www.cam.ac.uk"
  }
]
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法1: 运行主程序（推荐）

```bash
python main.py
```

这将执行完整的爬取流程，包括：
1. 爬取大学基础数据
2. 处理和补充大学信息
3. 保存为JSON格式

### 方法2: 使用API爬虫

```bash
python qs_api_scraper.py
```

这个版本尝试直接调用QS网站的API接口获取数据。

### 方法3: 使用Selenium爬虫

```bash
python qs_scraper.py
```

这个版本使用Selenium模拟浏览器行为，适用于JavaScript渲染的页面。

## 文件说明

- `main.py` - 主程序入口
- `qs_scraper.py` - 主要爬虫逻辑（支持requests和Selenium）
- `qs_api_scraper.py` - API爬虫（尝试直接调用API）
- `data_processor.py` - 数据处理模块
- `config.py` - 配置文件（国家洲际映射、爬虫参数等）
- `requirements.txt` - Python依赖包列表

## 配置说明

### 爬虫配置

在 `config.py` 中可以修改以下配置：

```python
SCRAPER_CONFIG = {
    "base_url": "https://www.qschina.cn/university-rankings/world-university-rankings/2025",
    "timeout": 30,
    "max_retries": 3,
    "delay_between_requests": 1,  # 请求间隔（秒）
}
```

### 输出配置

```python
OUTPUT_CONFIG = {
    "json_file": "qs_top1000_universities.json",
    "encoding": "utf-8",
    "ensure_ascii": False,
    "indent": 2,
}
```

## 爬取策略

程序采用多种策略确保数据获取成功：

1. **requests + BeautifulSoup**: 快速轻量，适用于静态内容
2. **Selenium WebDriver**: 处理JavaScript渲染的动态内容
3. **API调用**: 直接调用网站API接口
4. **页面源码解析**: 从HTML源码中提取JSON数据
5. **模拟数据**: 当所有方法都失败时，提供示例数据

## 数据处理功能

### 自动补充信息

- **大学缩写**: 自动生成或查找常见大学缩写
- **洲际信息**: 根据国家自动匹配洲际
- **官方网站**: 尝试查找大学官方网站
- **学院网站**: 查找包含学院、专业、课程信息的页面

### 数据清理

- 移除排名数字前缀
- 标准化大学名称格式
- 处理特殊字符和空格

## 注意事项

1. **请求频率**: 程序内置了请求延迟，避免对目标网站造成过大压力
2. **网站变化**: 如果QS网站结构发生变化，可能需要更新选择器
3. **网络环境**: 确保网络连接稳定，某些网站可能需要科学上网
4. **Chrome浏览器**: Selenium需要Chrome浏览器，程序会自动下载ChromeDriver

## 日志文件

程序运行时会生成 `qs_scraper.log` 日志文件，记录详细的执行过程和错误信息。

## 故障排除

### 常见问题

1. **ChromeDriver问题**
   ```bash
   # 手动安装ChromeDriver
   pip install webdriver-manager
   ```

2. **网络超时**
   - 检查网络连接
   - 增加timeout配置值
   - 使用代理（如需要）

3. **数据格式错误**
   - 检查QS网站是否更新了页面结构
   - 查看日志文件了解具体错误

### 调试模式

在代码中设置日志级别为DEBUG：

```python
logging.basicConfig(level=logging.DEBUG)
```

## 扩展功能

### 添加新的数据源

可以在 `qs_scraper.py` 中添加新的爬取方法：

```python
def scrape_from_new_source(self):
    # 实现新的数据源爬取逻辑
    pass
```

### 自定义数据处理

在 `data_processor.py` 中添加新的处理逻辑：

```python
def custom_process(self, data):
    # 自定义数据处理逻辑
    return processed_data
```

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的robots.txt和使用条款。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

- v1.0.0: 初始版本，支持基本的QS排名爬取功能
- 支持多种爬取策略
- 自动数据处理和补充
- 标准化JSON输出格式
