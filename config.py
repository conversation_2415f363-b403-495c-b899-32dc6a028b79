# -*- coding: utf-8 -*-
"""
配置文件 - 包含国家与洲际映射关系和其他配置信息
"""

# 国家与洲际的映射关系
COUNTRY_CONTINENT_MAP = {
    # 北美洲
    "United States": "North America",
    "Canada": "North America",
    "Mexico": "North America",
    
    # 欧洲
    "United Kingdom": "Europe",
    "Germany": "Europe",
    "France": "Europe",
    "Switzerland": "Europe",
    "Netherlands": "Europe",
    "Sweden": "Europe",
    "Denmark": "Europe",
    "Norway": "Europe",
    "Finland": "Europe",
    "Belgium": "Europe",
    "Austria": "Europe",
    "Italy": "Europe",
    "Spain": "Europe",
    "Ireland": "Europe",
    "Portugal": "Europe",
    "Poland": "Europe",
    "Czech Republic": "Europe",
    "Russia": "Europe",
    "Greece": "Europe",
    "Hungary": "Europe",
    "Slovenia": "Europe",
    "Estonia": "Europe",
    "Latvia": "Europe",
    "Lithuania": "Europe",
    "Croatia": "Europe",
    "Slovakia": "Europe",
    "Romania": "Europe",
    "Bulgaria": "Europe",
    "Cyprus": "Europe",
    "Malta": "Europe",
    "Luxembourg": "Europe",
    "Iceland": "Europe",
    
    # 亚洲
    "China": "Asia",
    "Japan": "Asia",
    "South Korea": "Asia",
    "Singapore": "Asia",
    "Hong Kong": "Asia",
    "Taiwan": "Asia",
    "India": "Asia",
    "Malaysia": "Asia",
    "Thailand": "Asia",
    "Indonesia": "Asia",
    "Philippines": "Asia",
    "Vietnam": "Asia",
    "Israel": "Asia",
    "Turkey": "Asia",
    "United Arab Emirates": "Asia",
    "Saudi Arabia": "Asia",
    "Qatar": "Asia",
    "Kuwait": "Asia",
    "Lebanon": "Asia",
    "Jordan": "Asia",
    "Iran": "Asia",
    "Pakistan": "Asia",
    "Bangladesh": "Asia",
    "Sri Lanka": "Asia",
    "Kazakhstan": "Asia",
    "Uzbekistan": "Asia",
    "Mongolia": "Asia",
    "Myanmar": "Asia",
    "Cambodia": "Asia",
    "Laos": "Asia",
    "Brunei": "Asia",
    "Macao": "Asia",
    
    # 大洋洲
    "Australia": "Oceania",
    "New Zealand": "Oceania",
    "Fiji": "Oceania",
    
    # 南美洲
    "Brazil": "South America",
    "Argentina": "South America",
    "Chile": "South America",
    "Colombia": "South America",
    "Peru": "South America",
    "Ecuador": "South America",
    "Uruguay": "South America",
    "Venezuela": "South America",
    "Bolivia": "South America",
    "Paraguay": "South America",
    "Guyana": "South America",
    "Suriname": "South America",
    
    # 非洲
    "South Africa": "Africa",
    "Egypt": "Africa",
    "Morocco": "Africa",
    "Tunisia": "Africa",
    "Algeria": "Africa",
    "Kenya": "Africa",
    "Uganda": "Africa",
    "Ghana": "Africa",
    "Nigeria": "Africa",
    "Ethiopia": "Africa",
    "Zimbabwe": "Africa",
    "Botswana": "Africa",
    "Namibia": "Africa",
    "Zambia": "Africa",
    "Tanzania": "Africa",
    "Rwanda": "Africa",
    "Senegal": "Africa",
    "Cameroon": "Africa",
    "Ivory Coast": "Africa",
    "Mali": "Africa",
    "Burkina Faso": "Africa",
    "Madagascar": "Africa",
    "Mauritius": "Africa",
}

# 爬虫配置
SCRAPER_CONFIG = {
    "base_url": "https://www.qschina.cn/university-rankings/world-university-rankings/2025",
    "headers": {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
    },
    "timeout": 30,
    "max_retries": 3,
    "delay_between_requests": 1,  # 秒
}

# 输出配置
OUTPUT_CONFIG = {
    "json_file": "qs_top1000_universities.json",
    "encoding": "utf-8",
    "ensure_ascii": False,
    "indent": 2,
}

# 大学缩写映射（常见的大学缩写）
UNIVERSITY_ABBREVIATIONS = {
    "Massachusetts Institute of Technology": "MIT",
    "Stanford University": "Stanford",
    "Harvard University": "Harvard",
    "California Institute of Technology": "Caltech",
    "University of Oxford": "Oxford",
    "University of Cambridge": "Cambridge",
    "ETH Zurich": "ETH",
    "University College London": "UCL",
    "Imperial College London": "Imperial",
    "University of Chicago": "UChicago",
    "National University of Singapore": "NUS",
    "Peking University": "PKU",
    "University of Pennsylvania": "UPenn",
    "Tsinghua University": "Tsinghua",
    "University of Edinburgh": "Edinburgh",
    "Princeton University": "Princeton",
    "Yale University": "Yale",
    "King's College London": "KCL",
    "London School of Economics and Political Science": "LSE",
    "University of Toronto": "UofT",
    "McGill University": "McGill",
    "University of Michigan": "UMich",
    "Australian National University": "ANU",
    "University of Hong Kong": "HKU",
    "University of British Columbia": "UBC",
    "University of Tokyo": "UTokyo",
    "École Polytechnique Fédérale de Lausanne": "EPFL",
    "Technical University of Munich": "TUM",
    "New York University": "NYU",
    "London School of Economics": "LSE",
    "University of California, Berkeley": "UC Berkeley",
    "University of Manchester": "Manchester",
    "University of California, Los Angeles": "UCLA",
    "University of Sydney": "USYD",
    "University of Melbourne": "UniMelb",
    "Nanyang Technological University": "NTU",
    "Delft University of Technology": "TU Delft",
    "University of Bristol": "Bristol",
    "Brown University": "Brown",
    "University of Amsterdam": "UvA",
    "University of Warwick": "Warwick",
    "Ludwig-Maximilians-Universität München": "LMU Munich",
    "University of Glasgow": "Glasgow",
    "Seoul National University": "SNU",
    "Durham University": "Durham",
    "University of Sheffield": "Sheffield",
    "University of Birmingham": "Birmingham",
    "University of Leeds": "Leeds",
    "University of Nottingham": "Nottingham",
    "University of Southampton": "Southampton",
    "Boston University": "BU",
    "University of St Andrews": "St Andrews",
    "Trinity College Dublin": "TCD",
    "Georgetown University": "Georgetown",
    "University of Zurich": "UZH",
}
