# -*- coding: utf-8 -*-
"""
QS大学排名API爬虫 - 直接调用QS网站的API接口
"""

import requests
import json
import time
import logging
from data_processor import DataProcessor
from config import OUTPUT_CONFIG, COUNTRY_CONTINENT_MAP

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QSAPIScraper:
    """QS API爬虫类"""

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://www.qschina.cn/university-rankings/world-university-rankings/2025',
            'X-Requested-With': 'XMLHttpRequest'
        })

        # QS API端点（需要通过浏览器开发者工具找到实际的API地址）
        self.api_endpoints = [
            'https://www.qschina.cn/sites/default/files/qs-rankings/2025/qs-world-university-rankings-2025.json',
            'https://www.topuniversities.com/sites/default/files/qs-rankings/2025/qs-world-university-rankings-2025.json',
            'https://api.qschina.cn/rankings/world-university-rankings/2025',
        ]

    def fetch_from_api(self):
        """从API获取数据"""
        for endpoint in self.api_endpoints:
            try:
                logger.info(f"尝试从API获取数据: {endpoint}")
                response = self.session.get(endpoint, timeout=30)

                if response.status_code == 200:
                    data = response.json()
                    if isinstance(data, list) and len(data) > 100:
                        logger.info(f"成功从API获取 {len(data)} 条数据")
                        return data
                    elif isinstance(data, dict) and 'universities' in data:
                        universities = data['universities']
                        logger.info(f"成功从API获取 {len(universities)} 条数据")
                        return universities

            except Exception as e:
                logger.warning(f"API {endpoint} 请求失败: {e}")
                continue

        return None

    def scrape_from_page_source(self):
        """从页面源码中提取JSON数据"""
        try:
            logger.info("尝试从页面源码提取数据...")

            # 获取页面内容
            response = self.session.get('https://www.qschina.cn/university-rankings/world-university-rankings/2025')
            response.raise_for_status()

            content = response.text

            # 查找页面中的JSON数据
            import re

            # 常见的JSON数据模式
            patterns = [
                r'var\s+rankingData\s*=\s*(\[.*?\]);',
                r'window\.rankingData\s*=\s*(\[.*?\]);',
                r'"universities":\s*(\[.*?\])',
                r'data-universities="([^"]*)"',
                r'rankings:\s*(\[.*?\])',
            ]

            for pattern in patterns:
                matches = re.findall(pattern, content, re.DOTALL)
                if matches:
                    try:
                        json_str = matches[0]
                        # 处理HTML实体编码
                        json_str = json_str.replace('&quot;', '"').replace('&amp;', '&')
                        data = json.loads(json_str)
                        if isinstance(data, list) and len(data) > 10:
                            logger.info(f"从页面源码提取到 {len(data)} 条数据")
                            return data
                    except json.JSONDecodeError:
                        continue

            logger.warning("未能从页面源码中提取到有效的JSON数据")
            return None

        except Exception as e:
            logger.error(f"从页面源码提取数据失败: {e}")
            return None

    def create_mock_data(self):
        """创建模拟数据用于演示"""
        logger.info("创建模拟数据用于演示...")

        # 基于真实的QS排名创建模拟数据 - 扩展到更多大学
        mock_universities = [
            {"name": "Massachusetts Institute of Technology", "rank": 1, "country": "United States"},
            {"name": "Stanford University", "rank": 2, "country": "United States"},
            {"name": "University of Oxford", "rank": 3, "country": "United Kingdom"},
            {"name": "Harvard University", "rank": 4, "country": "United States"},
            {"name": "University of Cambridge", "rank": 5, "country": "United Kingdom"},
            {"name": "Imperial College London", "rank": 6, "country": "United Kingdom"},
            {"name": "University College London", "rank": 7, "country": "United Kingdom"},
            {"name": "ETH Zurich", "rank": 8, "country": "Switzerland"},
            {"name": "University of Chicago", "rank": 9, "country": "United States"},
            {"name": "National University of Singapore", "rank": 10, "country": "Singapore"},
            {"name": "Peking University", "rank": 11, "country": "China"},
            {"name": "University of Pennsylvania", "rank": 12, "country": "United States"},
            {"name": "Tsinghua University", "rank": 13, "country": "China"},
            {"name": "University of Edinburgh", "rank": 14, "country": "United Kingdom"},
            {"name": "Princeton University", "rank": 15, "country": "United States"},
            {"name": "Yale University", "rank": 16, "country": "United States"},
            {"name": "King's College London", "rank": 17, "country": "United Kingdom"},
            {"name": "University of Toronto", "rank": 18, "country": "Canada"},
            {"name": "Columbia University", "rank": 19, "country": "United States"},
            {"name": "University of California, Berkeley", "rank": 20, "country": "United States"},
            {"name": "University of Tokyo", "rank": 21, "country": "Japan"},
            {"name": "University of Michigan", "rank": 22, "country": "United States"},
            {"name": "Australian National University", "rank": 23, "country": "Australia"},
            {"name": "University of Hong Kong", "rank": 24, "country": "Hong Kong"},
            {"name": "University of British Columbia", "rank": 25, "country": "Canada"},
            {"name": "McGill University", "rank": 26, "country": "Canada"},
            {"name": "École Polytechnique Fédérale de Lausanne", "rank": 27, "country": "Switzerland"},
            {"name": "Technical University of Munich", "rank": 28, "country": "Germany"},
            {"name": "New York University", "rank": 29, "country": "United States"},
            {"name": "London School of Economics", "rank": 30, "country": "United Kingdom"},
            {"name": "University of Manchester", "rank": 31, "country": "United Kingdom"},
            {"name": "University of California, Los Angeles", "rank": 32, "country": "United States"},
            {"name": "University of Sydney", "rank": 33, "country": "Australia"},
            {"name": "University of Melbourne", "rank": 34, "country": "Australia"},
            {"name": "Nanyang Technological University", "rank": 35, "country": "Singapore"},
            {"name": "Delft University of Technology", "rank": 36, "country": "Netherlands"},
            {"name": "University of Bristol", "rank": 37, "country": "United Kingdom"},
            {"name": "Brown University", "rank": 38, "country": "United States"},
            {"name": "University of Amsterdam", "rank": 39, "country": "Netherlands"},
            {"name": "University of Warwick", "rank": 40, "country": "United Kingdom"},
            {"name": "Ludwig-Maximilians-Universität München", "rank": 41, "country": "Germany"},
            {"name": "University of Glasgow", "rank": 42, "country": "United Kingdom"},
            {"name": "Seoul National University", "rank": 43, "country": "South Korea"},
            {"name": "Durham University", "rank": 44, "country": "United Kingdom"},
            {"name": "University of Sheffield", "rank": 45, "country": "United Kingdom"},
            {"name": "University of Birmingham", "rank": 46, "country": "United Kingdom"},
            {"name": "University of Leeds", "rank": 47, "country": "United Kingdom"},
            {"name": "University of Nottingham", "rank": 48, "country": "United Kingdom"},
            {"name": "University of Southampton", "rank": 49, "country": "United Kingdom"},
            {"name": "Boston University", "rank": 50, "country": "United States"},
        ]

        # 扩展到更多大学（模拟1000所）
        extended_universities = []
        for i in range(1000):
            if i < len(mock_universities):
                uni = mock_universities[i].copy()
            else:
                # 生成更多模拟数据
                uni = {
                    "name": f"University {i+1}",
                    "rank": i + 1,
                    "country": list(COUNTRY_CONTINENT_MAP.keys())[i % len(COUNTRY_CONTINENT_MAP)]
                }

            extended_universities.append({
                "name": uni["name"],
                "qs_ranking": uni["rank"],
                "country": uni["country"],
                "official_website": "",
                "college_website": ""
            })

        return extended_universities

    def scrape_universities(self):
        """主要的爬取方法"""
        logger.info("开始爬取QS大学排名数据...")

        # 方法1: 尝试API
        universities = self.fetch_from_api()

        # 方法2: 从页面源码提取
        if not universities:
            universities = self.scrape_from_page_source()

        # 方法3: 使用模拟数据
        if not universities:
            logger.warning("无法获取真实数据，使用模拟数据进行演示")
            universities = self.create_mock_data()

        # 标准化数据格式
        if universities:
            standardized = []
            for uni in universities:
                # 处理不同的数据格式
                if isinstance(uni, dict):
                    name = uni.get('name') or uni.get('university_name') or uni.get('institution')
                    rank = uni.get('rank') or uni.get('qs_ranking') or uni.get('position') or len(standardized) + 1
                    country = uni.get('country') or uni.get('location') or uni.get('nation') or ""

                    if name:
                        standardized.append({
                            "name": name,
                            "qs_ranking": int(rank) if str(rank).isdigit() else len(standardized) + 1,
                            "country": country,
                            "official_website": uni.get('official_website', ''),
                            "college_website": uni.get('college_website', '')
                        })

            universities = standardized

        logger.info(f"获取到 {len(universities) if universities else 0} 所大学的数据")
        return universities or []

def main():
    """测试函数"""
    scraper = QSAPIScraper()
    universities = scraper.scrape_universities()

    if universities:
        # 处理数据
        processor = DataProcessor()
        processed_universities = processor.batch_process_universities(universities[:50], delay=0.5)  # 只处理前50个作为演示

        # 保存数据
        with open('qs_sample_data.json', 'w', encoding='utf-8') as f:
            json.dump(processed_universities, f, ensure_ascii=False, indent=2)

        print(f"成功处理 {len(processed_universities)} 所大学的数据")
        print("数据已保存到 qs_sample_data.json")
    else:
        print("未能获取到大学数据")

if __name__ == "__main__":
    main()
